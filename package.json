{"name": "gomail-web", "private": true, "type": "module", "scripts": {"build": "react-router build && node scripts/fix-build-config.js", "cf-typegen": "wrangler types", "deploy": "pnpm run build && wrangler deploy", "dev": "react-router dev", "postinstall": "node scripts/init-project.js", "generate-configs": "node scripts/generate-configs.cjs", "typecheck": "pnpm run cf-typegen && react-router typegen && tsc -b", "db:generate": "drizzle-kit generate", "db:migrate": "wrangler d1 migrations apply gomail-database", "db:migrate:remote": "wrangler d1 migrations apply gomail-database --remote", "init-admin": "node scripts/init-admin.cjs", "init-project": "node scripts/init-project.js", "add-domain": "node scripts/add-domain.js", "remove-domain": "node scripts/remove-domain.js", "list-domains": "node -e \"console.log('🌐 当前配置的域名:'); const config = require('./config.cjs'); console.log('主域名:', config.domain.primary); console.log('额外域名:', config.domain.additional || []); console.log('支持的域名:', config.cloudflare.email.supportedDomains || []);\""}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@react-router/cloudflare": "^7.6.2", "@scaleway/random-name": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.2", "isbot": "^5.1.28", "lucide-react": "^0.512.0", "nanoid": "^5.1.5", "postal-mime": "^2.4.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.6.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@cloudflare/vite-plugin": "^1.5.0", "@react-router/dev": "^7.6.2", "@tailwindcss/vite": "^4.1.8", "@types/bcryptjs": "^3.0.0", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "drizzle-kit": "^0.31.1", "png-to-ico": "^2.1.8", "sharp": "^0.34.2", "tailwindcss": "^4.1.8", "to-ico": "^1.1.5", "tw-animate-css": "^1.3.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.19.1"}}