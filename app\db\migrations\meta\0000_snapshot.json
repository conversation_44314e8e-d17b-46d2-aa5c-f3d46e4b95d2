{"version": "6", "dialect": "sqlite", "id": "a75b06f7-cac4-481b-a6fb-fe557ffb43c5", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"attachments": {"name": "attachments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email_id": {"name": "email_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content_type": {"name": "content_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_inline": {"name": "is_inline", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "r2_key": {"name": "r2_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "r2_bucket": {"name": "r2_bucket", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "upload_status": {"name": "upload_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_attachments_email_id": {"name": "idx_attachments_email_id", "columns": ["email_id"], "isUnique": false}, "idx_attachments_r2_key": {"name": "idx_attachments_r2_key", "columns": ["r2_key"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "emails": {"name": "emails", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "mailbox_id": {"name": "mailbox_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "from_address": {"name": "from_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "to_address": {"name": "to_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "text_content": {"name": "text_content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "html_content": {"name": "html_content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "raw_email": {"name": "raw_email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "received_at": {"name": "received_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_read": {"name": "is_read", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_emails_mailbox_id": {"name": "idx_emails_mailbox_id", "columns": ["mailbox_id"], "isUnique": false}, "idx_emails_received_at": {"name": "idx_emails_received_at", "columns": ["received_at"], "isUnique": false}, "idx_emails_is_read": {"name": "idx_emails_is_read", "columns": ["is_read"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "mailboxes": {"name": "mailboxes", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {"mailboxes_email_unique": {"name": "mailboxes_email_unique", "columns": ["email"], "isUnique": true}, "idx_mailboxes_email": {"name": "idx_mailboxes_email", "columns": ["email"], "isUnique": false}, "idx_mailboxes_expires_at": {"name": "idx_mailboxes_expires_at", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}