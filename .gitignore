# 系统文件
.DS_Store
Thumbs.db

# 依赖
/node_modules/
*.tsbuildinfo

# React Router
/.react-router/
/build/
/auto-augment/

# Cloudflare
.mf
.wrangler
.dev.vars
wrangler.jsonc

# 环境变量和配置文件 (包含隐私信息)
.env
.env.local
.env.production
config.cjs
config.cjs


# 自动生成的配置文件 (包含隐私信息)
app/config/app.ts
DEPLOYMENT.md

# 但保留模板文件
!app/config/app.example.ts

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 编辑器
.vscode/
.idea/
*.swp
*.swo

# 临时文件
*.tmp
*.temp