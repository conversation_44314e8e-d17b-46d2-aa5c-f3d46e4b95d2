{"version": "6", "dialect": "sqlite", "id": "c32091d8-24b8-4855-8009-5afdda4923d4", "prevId": "a75b06f7-cac4-481b-a6fb-fe557ffb43c5", "tables": {"admins": {"name": "admins", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_login_at": {"name": "last_login_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"admins_username_unique": {"name": "admins_username_unique", "columns": ["username"], "isUnique": true}, "idx_admins_username": {"name": "idx_admins_username", "columns": ["username"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "api_tokens": {"name": "api_tokens", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_used_at": {"name": "last_used_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"api_tokens_token_unique": {"name": "api_tokens_token_unique", "columns": ["token"], "isUnique": true}, "idx_api_tokens_token": {"name": "idx_api_tokens_token", "columns": ["token"], "isUnique": false}, "idx_api_tokens_is_active": {"name": "idx_api_tokens_is_active", "columns": ["is_active"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "attachments": {"name": "attachments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email_id": {"name": "email_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content_type": {"name": "content_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_inline": {"name": "is_inline", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "r2_key": {"name": "r2_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "r2_bucket": {"name": "r2_bucket", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "upload_status": {"name": "upload_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_attachments_email_id": {"name": "idx_attachments_email_id", "columns": ["email_id"], "isUnique": false}, "idx_attachments_r2_key": {"name": "idx_attachments_r2_key", "columns": ["r2_key"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "emails": {"name": "emails", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "mailbox_id": {"name": "mailbox_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "from_address": {"name": "from_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "to_address": {"name": "to_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "text_content": {"name": "text_content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "html_content": {"name": "html_content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "raw_email": {"name": "raw_email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "received_at": {"name": "received_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_read": {"name": "is_read", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_emails_mailbox_id": {"name": "idx_emails_mailbox_id", "columns": ["mailbox_id"], "isUnique": false}, "idx_emails_received_at": {"name": "idx_emails_received_at", "columns": ["received_at"], "isUnique": false}, "idx_emails_is_read": {"name": "idx_emails_is_read", "columns": ["is_read"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "mailboxes": {"name": "mailboxes", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {"mailboxes_email_unique": {"name": "mailboxes_email_unique", "columns": ["email"], "isUnique": true}, "idx_mailboxes_email": {"name": "idx_mailboxes_email", "columns": ["email"], "isUnique": false}, "idx_mailboxes_expires_at": {"name": "idx_mailboxes_expires_at", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "token_usage_logs": {"name": "token_usage_logs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "token_id": {"name": "token_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_token_usage_logs_token_id": {"name": "idx_token_usage_logs_token_id", "columns": ["token_id"], "isUnique": false}, "idx_token_usage_logs_created_at": {"name": "idx_token_usage_logs_created_at", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}